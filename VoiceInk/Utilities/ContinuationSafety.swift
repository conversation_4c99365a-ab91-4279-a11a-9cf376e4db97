import Foundation

/// Utility class to help prevent double resume issues with CheckedContinuation
final class ContinuationSafety<T, E: Error> {
    private var hasResumed = false
    private let continuation: CheckedContinuation<T, E>
    
    init(_ continuation: CheckedContinuation<T, E>) {
        self.continuation = continuation
    }
    
    /// Safely resume with a value, preventing double resume
    func resume(returning value: T) {
        guard !hasResumed else {
            print("⚠️ Attempted to resume continuation multiple times with value, ignoring")
            return
        }
        hasResumed = true
        continuation.resume(returning: value)
    }
    
    /// Safely resume with an error, preventing double resume
    func resume(throwing error: E) {
        guard !hasResumed else {
            print("⚠️ Attempted to resume continuation multiple times with error, ignoring")
            return
        }
        hasResumed = true
        continuation.resume(throwing: error)
    }
    
    /// Check if continuation has already been resumed
    var isResumed: Bool {
        return hasResumed
    }
}

/// Extension for non-throwing continuations
final class ContinuationSafetyNonThrowing<T> {
    private var hasResumed = false
    private let continuation: CheckedContinuation<T, Never>
    
    init(_ continuation: CheckedContinuation<T, Never>) {
        self.continuation = continuation
    }
    
    /// Safely resume with a value, preventing double resume
    func resume(returning value: T) {
        guard !hasResumed else {
            print("⚠️ Attempted to resume non-throwing continuation multiple times, ignoring")
            return
        }
        hasResumed = true
        continuation.resume(returning: value)
    }
    
    /// Check if continuation has already been resumed
    var isResumed: Bool {
        return hasResumed
    }
}

/// Convenience functions for safe continuation handling
extension CheckedContinuation {
    /// Create a safe wrapper for this continuation
    func makeSafe() -> ContinuationSafety<T, E> where E: Error {
        return ContinuationSafety(self)
    }
}

extension CheckedContinuation where E == Never {
    /// Create a safe wrapper for this non-throwing continuation
    func makeSafe() -> ContinuationSafetyNonThrowing<T> {
        return ContinuationSafetyNonThrowing(self)
    }
}
