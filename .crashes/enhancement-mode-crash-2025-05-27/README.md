# Enhancement Mode Crash - May 27, 2025

## Issue Description
VoiceInk was crashing consistently after dictating with enhancement mode enabled. The crash was caused by `CheckedContinuation.resume()` being called multiple times on the same continuation.

## Crash Details
- **Date**: May 27, 2025
- **Time**: 12:49:23 -0600
- **VoiceInk Version**: 1.29 (129)
- **macOS Version**: 15.5 (24F74)
- **Crash Type**: EXC_BREAKPOINT (SIGTRAP)
- **Thread**: 18 (crashed)

## Root Cause
Double resume calls on CheckedContinuation objects in async/await code, specifically:
1. Screen capture service Vision framework text extraction
2. Download progress tracking with URLSession tasks
3. Task cancellation race conditions in enhancement mode
4. URLSession delegate multiple callbacks

## Files in this Directory

### crash-log.txt
The original crash log from macOS showing the stack trace and system information.

### CRASH_FIX_SUMMARY.md
Detailed documentation of:
- Problem analysis
- Root cause identification
- All code changes made
- Files modified
- Testing recommendations
- Prevention measures

## Resolution Status
✅ **FIXED** - All identified issues have been patched with proper continuation safety guards.

## Code Changes Applied
1. Added safety guards to prevent double resume in ScreenCaptureService
2. Fixed download progress tracking continuation issues
3. Improved task cancellation handling in AIEnhancementService
4. Added protection to TaskDelegate against multiple callbacks
5. Created ContinuationSafety utility classes for future use

## Verification
After applying the fixes:
- Enhancement mode works without crashes
- Screen capture context functions reliably
- Model downloads complete successfully
- Task cancellation is handled gracefully

## Related Files Modified
- `VoiceInk/Services/ScreenCaptureService.swift`
- `VoiceInk/Whisper/WhisperState+ModelManager.swift`
- `VoiceInk/Services/AIEnhancementService.swift`
- `VoiceInk/Whisper/WhisperState.swift`
- `VoiceInk/Utilities/ContinuationSafety.swift` (new)
