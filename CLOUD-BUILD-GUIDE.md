# Building VoiceInk in the Cloud

This guide shows you how to build VoiceInk using cloud-based macOS environments, perfect for developers who don't have access to a Mac.

## 🚀 Quick Start with GitHub Actions

### Prerequisites
- GitHub account (free)
- Git installed on your local machine

### Step 1: Fork the Repository
1. Go to the VoiceInk repository on GitHub
2. Click the "Fork" button to create your own copy
3. Clone your fork locally:
```bash
git clone https://github.com/YOUR_USERNAME/VoiceInk.git
cd VoiceInk
```

### Step 2: Enable GitHub Actions
The workflow file `.github/workflows/build-voiceink.yml` is already included in this repository.

1. Go to your forked repository on GitHub
2. Click the "Actions" tab
3. If prompted, click "I understand my workflows, go ahead and enable them"

### Step 3: Trigger a Build
You can trigger builds in several ways:

**Option A: Push to main branch**
```bash
git add .
git commit -m "Trigger cloud build"
git push origin main
```

**Option B: Manual trigger**
1. Go to Actions tab in your GitHub repository
2. Click "Build VoiceInk" workflow
3. Click "Run workflow" button

### Step 4: Monitor the Build
1. Go to the Actions tab in your repository
2. Click on the running workflow
3. Watch the real-time build logs
4. Download build artifacts when complete

## 📊 What the Cloud Build Does

The automated workflow:
1. ✅ Sets up macOS environment with latest Xcode
2. ✅ Clones and builds whisper.cpp framework
3. ✅ Links whisper.xcframework to VoiceInk
4. ✅ Resolves Swift Package Manager dependencies
5. ✅ Builds VoiceInk in Debug configuration
6. ✅ Runs automated tests
7. ✅ Builds VoiceInk in Release configuration
8. ✅ Archives build artifacts for download

## 💰 Cost Information

**GitHub Actions (Recommended)**
- ✅ **Free**: 2,000 minutes/month for public repositories
- ✅ **Private repos**: $0.08/minute (macOS runners)
- ✅ **Typical VoiceInk build**: ~10-15 minutes

**Alternative: Xcode Cloud**
- ✅ **Free tier**: 25 compute hours/month (requires Apple Developer Program $99/year)
- ✅ **Additional**: $49.99/month for 100 hours

## 🔧 Customizing the Build

### Adding Build Steps
Edit `.github/workflows/build-voiceink.yml` to add custom steps:

```yaml
- name: Custom Build Step
  run: |
    echo "Add your custom commands here"
    # Your custom build logic
```

### Building Specific Branches
The workflow triggers on:
- Pushes to `main` and `develop` branches
- Pull requests to `main`
- Manual triggers

### Code Signing (Advanced)
For distribution builds, you'll need to add code signing:

1. Add secrets to your GitHub repository:
   - `APPLE_CERTIFICATE_BASE64`
   - `APPLE_CERTIFICATE_PASSWORD`
   - `PROVISIONING_PROFILE_BASE64`

2. Modify the workflow to include signing steps

## 🐛 Troubleshooting

### Build Fails on whisper.cpp
- Check if whisper.cpp repository is accessible
- Verify the build script `./build-xcframework.sh` exists

### Xcode Build Errors
- Check Xcode version compatibility
- Verify Swift Package Manager dependencies
- Review build logs for specific error messages

### Framework Linking Issues
- Ensure whisper.xcframework is properly copied
- Check Xcode project settings for framework references

## 📱 Next Steps

Once your cloud build is working:

1. **Development Workflow**: Make changes locally, push to trigger builds
2. **Testing**: Use the automated test runner to verify changes
3. **Distribution**: Set up code signing for App Store or direct distribution
4. **CI/CD**: Extend the workflow for automated deployment

## 🆘 Getting Help

If you encounter issues:
1. Check the [GitHub Actions logs](https://docs.github.com/en/actions/monitoring-and-troubleshooting-workflows/using-workflow-run-logs)
2. Review the original [BUILDING.md](./BUILDING.md) for local build instructions
3. Open an issue in the VoiceInk repository
4. Check [GitHub Actions documentation](https://docs.github.com/en/actions)

## 🎯 Success!

You now have a fully automated cloud build system for VoiceInk that:
- ✅ Builds on every code change
- ✅ Runs on genuine macOS with Xcode
- ✅ Handles all dependencies automatically
- ✅ Provides downloadable build artifacts
- ✅ Costs nothing for public repositories

Happy coding! 🚀

---
*Last updated: Cloud build triggered via Augment Agent*
