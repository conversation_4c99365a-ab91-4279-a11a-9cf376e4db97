name: Build VoiceInk

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # Allow manual triggering

jobs:
  build:
    runs-on: macos-latest

    steps:
    - name: Checkout VoiceInk repository
      uses: actions/checkout@v4

    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable

    - name: Show Xcode version
      run: xcodebuild -version

    - name: <PERSON>lone and build whisper.cpp framework
      run: |
        echo "🔄 Cloning whisper.cpp..."
        git clone https://github.com/ggerganov/whisper.cpp.git
        cd whisper.cpp

        echo "🔨 Building whisper.cpp XCFramework..."
        ./build-xcframework.sh

        echo "✅ whisper.cpp build complete"
        ls -la build-apple/

    - name: Link whisper.xcframework to VoiceInk project
      run: |
        echo "🔗 Linking whisper.xcframework..."

        # Debug: Show current working directory and structure
        echo "Current working directory: $(pwd)"
        echo "Directory structure:"
        ls -la

        # Go back to the workspace root and create Downloads directory
        cd ..
        echo "Now in workspace root: $(pwd)"
        mkdir -p Downloads/build-apple

        # Copy the built framework to the expected path
        cp -R VoiceInk/whisper.cpp/build-apple/whisper.xcframework Downloads/build-apple/

        echo "✅ Framework linked successfully"
        echo "Framework location:"
        ls -la Downloads/build-apple/

        # Verify the framework is in the right place for Xcode
        echo "Verifying framework path for Xcode:"
        ls -la Downloads/build-apple/whisper.xcframework

    - name: Resolve Swift Package Dependencies
      run: |
        echo "📦 Resolving Swift Package Manager dependencies..."
        xcodebuild \
          -resolvePackageDependencies \
          -project VoiceInk.xcodeproj \
          -scheme VoiceInk \
          -destination 'platform=macOS'

    - name: Build VoiceInk (Debug)
      run: |
        echo "🔨 Building VoiceInk in Debug configuration..."
        xcodebuild \
          -project VoiceInk.xcodeproj \
          -scheme VoiceInk \
          -configuration Debug \
          -destination 'platform=macOS' \
          -sdk macosx \
          build \
          CODE_SIGNING_ALLOWED=NO \
          ONLY_ACTIVE_ARCH=YES

    - name: Run Tests
      run: |
        echo "🧪 Running VoiceInk tests..."
        xcodebuild \
          -project VoiceInk.xcodeproj \
          -scheme VoiceInk \
          -configuration Debug \
          -destination 'platform=macOS' \
          -sdk macosx \
          test \
          CODE_SIGNING_ALLOWED=NO \
          ONLY_ACTIVE_ARCH=YES
      continue-on-error: true  # Don't fail if tests fail

    - name: Build VoiceInk (Release)
      run: |
        echo "🚀 Building VoiceInk in Release configuration..."
        xcodebuild \
          -project VoiceInk.xcodeproj \
          -scheme VoiceInk \
          -configuration Release \
          -destination 'platform=macOS' \
          -sdk macosx \
          build \
          CODE_SIGNING_ALLOWED=NO \
          ONLY_ACTIVE_ARCH=YES

    - name: Archive build artifacts
      run: |
        echo "📦 Preparing build artifacts..."

        # Find and copy the built VoiceInk.app
        echo "🔍 Looking for built VoiceInk.app..."
        find ~/Library/Developer/Xcode/DerivedData -name "VoiceInk.app" -type d 2>/dev/null || echo "No VoiceInk.app found in DerivedData"

        # Create artifacts directory structure
        mkdir -p build/artifacts/Release
        mkdir -p build/artifacts/Debug
        mkdir -p build/frameworks

        # Copy framework
        cp -R ../Downloads/build-apple/whisper.xcframework build/frameworks/ || echo "Framework copy failed"

        # Copy Release build if it exists
        RELEASE_APP=$(find ~/Library/Developer/Xcode/DerivedData -path "*/Build/Products/Release/VoiceInk.app" -type d 2>/dev/null | head -1)
        if [ -n "$RELEASE_APP" ]; then
          echo "✅ Found Release build: $RELEASE_APP"
          cp -R "$RELEASE_APP" build/artifacts/Release/
        else
          echo "❌ No Release build found"
        fi

        # Copy Debug build if it exists
        DEBUG_APP=$(find ~/Library/Developer/Xcode/DerivedData -path "*/Build/Products/Debug/VoiceInk.app" -type d 2>/dev/null | head -1)
        if [ -n "$DEBUG_APP" ]; then
          echo "✅ Found Debug build: $DEBUG_APP"
          cp -R "$DEBUG_APP" build/artifacts/Debug/
        else
          echo "❌ No Debug build found"
        fi

        echo "📦 Build artifacts ready:"
        ls -la build/
        ls -la build/artifacts/ || echo "No artifacts directory"
        ls -la build/artifacts/Release/ || echo "No Release artifacts"
        ls -la build/artifacts/Debug/ || echo "No Debug artifacts"
        ls -la build/frameworks/ || echo "No frameworks directory"

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: voiceink-build-artifacts
        path: build/
        retention-days: 7

    - name: Build Summary
      run: |
        echo "🎉 Build completed successfully!"
        echo "📊 Build Summary:"
        echo "- Xcode version: $(xcodebuild -version | head -1)"
        echo "- macOS version: $(sw_vers -productVersion)"
        echo "- whisper.cpp framework: ✅ Built"
        echo "- VoiceInk Debug build: ✅ Completed"
        echo "- VoiceInk Release build: ✅ Completed"
