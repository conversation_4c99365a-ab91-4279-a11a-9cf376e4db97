#!/bin/bash

# Script to configure Code OSS for Cloud Shell to use Microsoft VSCode Marketplace
# This runs once when the Cloud Shell environment is created

echo "Setting up Microsoft VSCode Marketplace for Code OSS..."

# Path to the product.json file
PRODUCT_JSON="/google/devshell/editor/code-oss-for-cloud-shell/product.json"
BACKUP_FILE="$HOME/.customize_environment/product.json.backup"

# Create backup if it doesn't exist
if [ ! -f "$BACKUP_FILE" ]; then
    echo "Creating backup of original product.json..."
    cp "$PRODUCT_JSON" "$BACKUP_FILE"
fi

# Create the marketplace configuration script
cat > "$HOME/.customize_environment/configure_marketplace.sh" << 'EOF'
#!/bin/bash

# Script to modify product.json to use Microsoft VSCode Marketplace
PRODUCT_JSON="/google/devshell/editor/code-oss-for-cloud-shell/product.json"

# Check if file exists and is writable
if [ ! -w "$PRODUCT_JSON" ]; then
    echo "Warning: Cannot write to $PRODUCT_JSON - trying with sudo"
    SUDO_PREFIX="sudo"
else
    SUDO_PREFIX=""
fi

# Create a temporary file with the modified configuration
cat > /tmp/product_patch.json << 'PATCH_EOF'
{
    "extensionsGallery": {
        "serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery",
        "itemUrl": "https://marketplace.visualstudio.com/items",
        "resourceUrlTemplate": "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/{publisher}/vsextensions/{name}/{version}/vspackage",
        "controlUrl": "https://az764295.vo.msecnd.net/extensions/marketplace.json",
        "recommendationsUrl": "https://az764295.vo.msecnd.net/extensions/workspaceRecommendations.json.gz"
    }
}
PATCH_EOF

# Use jq to merge the configuration if available, otherwise use sed
if command -v jq >/dev/null 2>&1; then
    echo "Using jq to update marketplace configuration..."
    $SUDO_PREFIX jq -s '.[0] * .[1]' "$PRODUCT_JSON" /tmp/product_patch.json > /tmp/product_updated.json
    $SUDO_PREFIX cp /tmp/product_updated.json "$PRODUCT_JSON"
else
    echo "Using sed to update marketplace configuration..."
    # Backup current file
    $SUDO_PREFIX cp "$PRODUCT_JSON" "${PRODUCT_JSON}.bak"
    
    # Replace the extensionsGallery section
    $SUDO_PREFIX sed -i '/"extensionsGallery": {/,/},/c\
	"extensionsGallery": {\
		"serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery",\
		"itemUrl": "https://marketplace.visualstudio.com/items",\
		"resourceUrlTemplate": "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/{publisher}/vsextensions/{name}/{version}/vspackage",\
		"controlUrl": "https://az764295.vo.msecnd.net/extensions/marketplace.json",\
		"recommendationsUrl": "https://az764295.vo.msecnd.net/extensions/workspaceRecommendations.json.gz"\
	},' "$PRODUCT_JSON"
fi

# Clean up temporary files
rm -f /tmp/product_patch.json /tmp/product_updated.json

echo "Microsoft VSCode Marketplace configuration applied!"
echo "Note: You may need to restart the Code OSS server for changes to take effect."
EOF

# Make the script executable
chmod +x "$HOME/.customize_environment/configure_marketplace.sh"

echo "Marketplace configuration script created at ~/.customize_environment/configure_marketplace.sh"
echo "Installation complete!"
