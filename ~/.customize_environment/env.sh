#!/bin/bash

# This script runs on every Cloud Shell startup
# It ensures the Microsoft VSCode Marketplace configuration persists

# Check if the marketplace configuration script exists and run it
if [ -f "$HOME/.customize_environment/configure_marketplace.sh" ]; then
    # Check if product.json still has Open VSX configuration
    if grep -q "open-vsx.org" /google/devshell/editor/code-oss-for-cloud-shell/product.json 2>/dev/null; then
        echo "Applying Microsoft VSCode Marketplace configuration..."
        bash "$HOME/.customize_environment/configure_marketplace.sh"
    fi
fi
